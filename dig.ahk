#Persistent
#NoEnv
SetBatchLines, -1
SetTitleMatchMode, 2
CoordMode, Pixel, Screen
CoordMode, Mouse, Screen
CoordMode, ToolTip, Screen

; Change this to the full path of your uploaded target zone image
targetImage := "Z:\dig\target-zone.png" ; Replace this with your real path

; Search coordinates (left, top, right, bottom)
; You MUST update these to where the bar appears on your screen.
searchLeft := 500
searchTop := 500
searchRight := 1400
searchBottom := 600

Hotkey, F8, ToggleScript
return

ToggleScript:
    Toggle := !Toggle
    if (Toggle) {
        SetTimer, LookForZone, 20
        ToolTip, 🔍 Auto-Press Space: ON
    } else {
        SetTimer, LookForZone, Off
        ToolTip, ❌ OFF
        Sleep, 1000
        ToolTip
    }
return

LookForZone:
    ImageSearch, FoundX, FoundY, %searchLeft%, %searchTop%, %searchRight%, %searchBottom%, %targetImage%
    if (ErrorLevel = 0) {
        Send, {Space}
        ToolTip, 🎯 HIT SPACE at %FoundX%, %FoundY%
        Sleep, 500  ; prevent rapid firing
    } else {
        ToolTip, Looking...
    }
return
