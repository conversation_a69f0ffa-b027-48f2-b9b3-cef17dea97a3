#Persistent
#NoEnv
SetBatchLines, -1
SetTitleMatchMode, 2
CoordMode, Pixel, Screen
CoordMode, Mouse, Screen
CoordMode, ToolTip, Screen

; === CONFIGURATION SECTION ===
; You need to set these coordinates to match your game window
; The bar coordinates (where the indicator moves)
barLeft := 500      ; Left edge of the bar
barTop := 500       ; Top of the bar
barRight := 1400    ; Right edge of the bar
barBottom := 600    ; Bottom of the bar

; Target zone coordinates (the dark grey area where you need to press space)
targetZoneLeft := 700   ; Left edge of dark grey zone
targetZoneRight := 900  ; Right edge of dark grey zone

; Colors to detect (you may need to adjust these)
indicatorColor := 0x000000  ; Black color of the indicator (hex)
darkGreyColor := 0x404040   ; Dark grey color of target zone (hex)

; Detection settings
checkInterval := 10         ; How often to check (milliseconds)
cooldownTime := 200        ; Prevent rapid firing (milliseconds)

; === HOTKEYS ===
Hotkey, F8, ToggleScript   ; F8 to start/stop
Hotkey, F9, SetupCoords    ; F9 to help setup coordinates
return

ToggleScript:
    Toggle := !Toggle
    if (Toggle) {
        SetTimer, DetectIndicator, %checkInterval%
        ToolTip, 🔍 DIG Auto-Press: ON (F8 to stop, F9 for setup)
    } else {
        SetTimer, DetectIndicator, Off
        ToolTip, ❌ DIG Auto-Press: OFF
        Sleep, 1000
        ToolTip
    }
return

SetupCoords:
    ToolTip, Move mouse to LEFT edge of bar and press F10
    Hotkey, F10, SetBarLeft
return

SetBarLeft:
    MouseGetPos, barLeft, barTop
    ToolTip, Left set to %barLeft%`, %barTop%. Now move to RIGHT edge and press F11
    Hotkey, F10, Off
    Hotkey, F11, SetBarRight
return

SetBarRight:
    MouseGetPos, barRight, barBottom
    ToolTip, Bar area set: %barLeft%`, %barTop% to %barRight%`, %barBottom%. Move to LEFT edge of target zone and press F12
    Hotkey, F11, Off
    Hotkey, F12, SetTargetLeft
return

SetTargetLeft:
    MouseGetPos, targetZoneLeft, temp
    ToolTip, Target left set to %targetZoneLeft%. Move to RIGHT edge of target zone and press END
    Hotkey, F12, Off
    Hotkey, End, SetTargetRight
return

SetTargetRight:
    MouseGetPos, targetZoneRight, temp
    ToolTip, Setup complete! Target zone: %targetZoneLeft% to %targetZoneRight%. Press F8 to start detection.
    Hotkey, End, Off
    Sleep, 3000
    ToolTip
return

DetectIndicator:
    ; Look for the black indicator in the bar area
    indicatorX := -1

    ; Scan horizontally across the bar to find the indicator
    Loop, % (barRight - barLeft) {
        currentX := barLeft + A_Index
        currentY := barTop + ((barBottom - barTop) / 2)  ; Middle of bar height

        PixelGetColor, pixelColor, %currentX%, %currentY%

        ; Check if this pixel matches the indicator color (with some tolerance)
        if (ColorMatch(pixelColor, indicatorColor, 30)) {
            indicatorX := currentX
            break
        }
    }

    ; If we found the indicator, check if it's in the target zone
    if (indicatorX > 0 && indicatorX >= targetZoneLeft && indicatorX <= targetZoneRight) {
        Send, {Space}
        ToolTip, 🎯 SPACE pressed! Indicator at %indicatorX%
        Sleep, %cooldownTime%  ; Prevent rapid firing
    } else if (indicatorX > 0) {
        ToolTip, 🔍 Indicator at %indicatorX% (target: %targetZoneLeft%-%targetZoneRight%)
    } else {
        ToolTip, 🔍 Searching for indicator...
    }
return

; Helper function to check if two colors match within tolerance
ColorMatch(color1, color2, tolerance) {
    r1 := (color1 >> 16) & 0xFF
    g1 := (color1 >> 8) & 0xFF
    b1 := color1 & 0xFF

    r2 := (color2 >> 16) & 0xFF
    g2 := (color2 >> 8) & 0xFF
    b2 := color2 & 0xFF

    return (Abs(r1 - r2) <= tolerance && Abs(g1 - g2) <= tolerance && Abs(b1 - b2) <= tolerance)
}
