#!/usr/bin/env python3
"""
DIG Game Macro - Template Matching Version
Uses reference images to detect the bar, indicator, and target zone
"""

import cv2
import numpy as np
import pyautogui
import time
import keyboard
import threading
from PIL import ImageGrab
import os

class DIGMacro:
    def __init__(self):
        self.running = False
        self.check_interval = 0.02  # 20ms
        self.cooldown_time = 0.3    # 300ms cooldown
        self.last_press_time = 0

        # Template images (you'll need to create these)
        self.bar_template = None
        self.indicator_template = None
        self.target_zone_template = None

        # Load templates
        self.load_templates()

        print("DIG Macro initialized!")
        print("Controls:")
        print("  F8 - Start/Stop macro")
        print("  ESC - Exit program")
        print()
        if not self.templates_loaded():
            print("⚠️  Missing template images! Please create:")
            print("  - bar.png (screenshot of the entire bar)")
            print("  - indicator.png (screenshot of just the black indicator)")
            print("  - target_zone.png (screenshot of the dark grey target area)")
        else:
            print("✓ All template images loaded!")

    def load_templates(self):
        """Load template images for matching"""
        try:
            if os.path.exists("bar.png"):
                self.bar_template = cv2.imread("bar.png", cv2.IMREAD_COLOR)
            if os.path.exists("indicator.png"):
                self.indicator_template = cv2.imread("indicator.png", cv2.IMREAD_COLOR)
            if os.path.exists("target_zone.png"):
                self.target_zone_template = cv2.imread("target_zone.png", cv2.IMREAD_COLOR)
        except Exception as e:
            print(f"Error loading templates: {e}")

    def templates_loaded(self):
        """Check if all required templates are loaded"""
        return (self.bar_template is not None and
                self.indicator_template is not None and
                self.target_zone_template is not None)

    def find_template(self, screenshot, template, threshold=0.8):
        """Find template in screenshot using template matching"""
        if template is None:
            return None

        # Check if template is larger than screenshot
        screen_h, screen_w = screenshot.shape[:2]
        template_h, template_w = template.shape[:2]

        if template_h > screen_h or template_w > screen_w:
            print(f"Template ({template_w}x{template_h}) larger than search area ({screen_w}x{screen_h})")
            return None

        try:
            result = cv2.matchTemplate(screenshot, template, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

            if max_val >= threshold:
                h, w = template.shape[:2]
                return (max_loc[0], max_loc[1], w, h, max_val)
        except cv2.error as e:
            print(f"Template matching error: {e}")
            return None

        return None

    def capture_screen(self):
        """Capture the entire screen"""
        screenshot = ImageGrab.grab()
        return cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)

    def find_bar_and_elements(self, screenshot):
        """Find the bar, indicator, and target zone in the screenshot"""
        # Try to find the bar first
        bar_match = self.find_template(screenshot, self.bar_template, threshold=0.6)

        # If we can't find the exact bar, try to find indicator and target zone directly on full screen
        if not bar_match:
            # Search for indicator and target zone on full screen with lower thresholds
            indicator_match = self.find_template(screenshot, self.indicator_template, threshold=0.7)
            target_match = self.find_template(screenshot, self.target_zone_template, threshold=0.6)
            return None, indicator_match, target_match

        bar_x, bar_y, bar_w, bar_h, _ = bar_match

        # Extract the bar region for more precise detection
        bar_region = screenshot[bar_y:bar_y+bar_h, bar_x:bar_x+bar_w]

        print(f"Bar region size: {bar_region.shape}, Bar template size: {self.bar_template.shape}")

        # Find indicator within the bar region (with fallback to full screen)
        indicator_match = self.find_template(bar_region, self.indicator_template, threshold=0.5)
        if not indicator_match:
            # Fallback: search indicator on full screen
            indicator_match = self.find_template(screenshot, self.indicator_template, threshold=0.7)

        # Find target zone within the bar region (with fallback to full screen)
        target_match = self.find_template(bar_region, self.target_zone_template, threshold=0.5)
        if not target_match:
            # Fallback: search target zone on full screen
            target_match = self.find_template(screenshot, self.target_zone_template, threshold=0.6)

        return bar_match, indicator_match, target_match

    def is_indicator_in_target(self, indicator_match, target_match, bar_match=None):
        """Check if indicator overlaps with target zone"""
        if not indicator_match or not target_match:
            return False

        ind_x, ind_y, ind_w, ind_h, _ = indicator_match
        target_x, target_y, target_w, target_h, _ = target_match

        # If we have a bar match, adjust coordinates relative to bar position
        if bar_match:
            bar_x, bar_y, _, _, _ = bar_match
            # If coordinates are relative to bar region, adjust them
            if ind_x < 100 and target_x < 100:  # Likely relative coordinates
                ind_x += bar_x
                ind_y += bar_y
                target_x += bar_x
                target_y += bar_y

        # Check if indicator center is within target zone (with some tolerance)
        ind_center_x = ind_x + ind_w // 2
        ind_center_y = ind_y + ind_h // 2

        # Add some tolerance for overlap detection
        tolerance = 10

        overlap_x = (target_x - tolerance <= ind_center_x <= target_x + target_w + tolerance)
        overlap_y = (target_y - tolerance <= ind_center_y <= target_y + target_h + tolerance)

        if overlap_x and overlap_y:
            print(f"MATCH! Indicator center ({ind_center_x}, {ind_center_y}) in target zone ({target_x}, {target_y}, {target_w}x{target_h})")
            return True

        return False
    
    def press_space(self):
        """Press space with cooldown protection"""
        current_time = time.time()
        if current_time - self.last_press_time >= self.cooldown_time:
            pyautogui.press('space')
            self.last_press_time = current_time
            print(f"SPACE pressed! Time: {current_time:.2f}")
            return True
        return False
    
    def detection_loop(self):
        """Main detection loop using template matching"""
        print("Detection started! Looking for bar and indicator...")

        while self.running:
            try:
                if not self.templates_loaded():
                    print("Missing template images! Please create bar.png, indicator.png, and target_zone.png")
                    break

                # Capture full screen
                screenshot = self.capture_screen()

                # Find bar and elements
                bar_match, indicator_match, target_match = self.find_bar_and_elements(screenshot)

                if bar_match and indicator_match and target_match:
                    # Check if indicator is in target zone
                    if self.is_indicator_in_target(indicator_match, target_match):
                        if self.press_space():
                            print("✓ SPACE pressed! Indicator in target zone!")
                    else:
                        print("Indicator found, waiting for target zone...", end='\r')
                elif bar_match and indicator_match:
                    print("Bar and indicator found, looking for target zone...", end='\r')
                elif bar_match:
                    print("Bar found, looking for indicator...", end='\r')
                else:
                    print("Looking for game bar...", end='\r')

                time.sleep(self.check_interval)

            except Exception as e:
                print(f"Error in detection loop: {e}")
                break

        print("\nDetection stopped.")
    
    def toggle_macro(self):
        """Start or stop the macro"""
        if not self.running:
            if not self.templates_loaded():
                print("Please create template images first!")
                print("You need: bar.png, indicator.png, target_zone.png")
                return

            self.running = True
            self.detection_thread = threading.Thread(target=self.detection_loop)
            self.detection_thread.daemon = True
            self.detection_thread.start()
            print("Macro STARTED!")
        else:
            self.running = False
            print("Macro STOPPED!")

    def run(self):
        """Main run loop with keyboard controls"""
        print("\nDIG Macro is ready!")
        print("Press F8 to start/stop, ESC to exit")

        # Setup keyboard hooks
        keyboard.add_hotkey('f8', self.toggle_macro)

        try:
            # Keep the program running
            keyboard.wait('esc')
        except KeyboardInterrupt:
            pass
        finally:
            self.running = False
            print("\nExiting DIG Macro...")

def main():
    # Disable pyautogui failsafe for gaming
    pyautogui.FAILSAFE = False

    macro = DIGMacro()
    macro.run()

if __name__ == "__main__":
    main()
