#!/usr/bin/env python3
"""
DIG Game Macro - Python Version
Detects when the black indicator reaches the target zone and presses SPACE
"""

import cv2
import numpy as np
import pyautogui
import time
import keyboard
import threading
from PIL import ImageGrab
import argparse

class DIGMacro:
    def __init__(self):
        self.running = False
        self.setup_mode = False
        self.bar_region = None
        self.target_zone = None
        self.check_interval = 0.01  # 10ms
        self.cooldown_time = 0.2    # 200ms cooldown
        self.last_press_time = 0
        
        # Color ranges for detection (HSV)
        self.black_lower = np.array([0, 0, 0])
        self.black_upper = np.array([180, 255, 50])  # Dark colors
        
        print("DIG Macro initialized!")
        print("Controls:")
        print("  F8 - Start/Stop macro")
        print("  F9 - Setup coordinates")
        print("  ESC - Exit program")
        
    def setup_coordinates(self):
        """Interactive setup for bar and target zone coordinates"""
        print("\n=== COORDINATE SETUP ===")
        print("We'll capture the bar area and target zone.")
        print("Make sure the DIG game is visible on screen.")
        
        input("Press ENTER when ready to capture the bar area...")
        
        # Capture full screen
        screenshot = ImageGrab.grab()
        screenshot_np = np.array(screenshot)
        
        # Convert to OpenCV format (BGR)
        screenshot_cv = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)
        
        # Let user select bar region
        print("Select the ENTIRE bar area (from left to right edge)")
        self.bar_region = cv2.selectROI("Select Bar Area", screenshot_cv, False)
        cv2.destroyWindow("Select Bar Area")
        
        if self.bar_region[2] == 0 or self.bar_region[3] == 0:
            print("Invalid selection. Setup cancelled.")
            return False
            
        print("Select the TARGET ZONE (dark grey area where you need to press SPACE)")
        self.target_zone = cv2.selectROI("Select Target Zone", screenshot_cv, False)
        cv2.destroyWindow("Select Target Zone")
        
        if self.target_zone[2] == 0 or self.target_zone[3] == 0:
            print("Invalid selection. Setup cancelled.")
            return False
        
        print(f"Bar region: {self.bar_region}")
        print(f"Target zone: {self.target_zone}")
        print("Setup complete! Press F8 to start the macro.")
        return True
    
    def capture_screen_region(self, region):
        """Capture a specific region of the screen"""
        x, y, w, h = region
        screenshot = ImageGrab.grab(bbox=(x, y, x + w, y + h))
        return np.array(screenshot)
    
    def find_indicator_position(self, bar_image):
        """Find the x-position of the black indicator in the bar"""
        # Convert to HSV for better color detection
        hsv = cv2.cvtColor(bar_image, cv2.COLOR_RGB2HSV)
        
        # Create mask for black/dark colors
        mask = cv2.inRange(hsv, self.black_lower, self.black_upper)
        
        # Find contours
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if not contours:
            return None
            
        # Find the largest contour (likely the indicator)
        largest_contour = max(contours, key=cv2.contourArea)
        
        # Get bounding box
        x, y, w, h = cv2.boundingRect(largest_contour)
        
        # Return center x position of the indicator
        return x + w // 2
    
    def is_in_target_zone(self, indicator_x):
        """Check if the indicator is in the target zone"""
        if not self.target_zone or indicator_x is None:
            return False
            
        # Convert target zone coordinates relative to bar region
        bar_x = self.bar_region[0]
        target_start = self.target_zone[0] - bar_x
        target_end = target_start + self.target_zone[2]
        
        return target_start <= indicator_x <= target_end
    
    def press_space(self):
        """Press space with cooldown protection"""
        current_time = time.time()
        if current_time - self.last_press_time >= self.cooldown_time:
            pyautogui.press('space')
            self.last_press_time = current_time
            print(f"SPACE pressed! Time: {current_time:.2f}")
            return True
        return False
    
    def detection_loop(self):
        """Main detection loop"""
        print("Detection started! Monitoring for indicator...")
        
        while self.running:
            try:
                if not self.bar_region:
                    print("No bar region set. Please run setup first (F9)")
                    break
                
                # Capture bar area
                bar_image = self.capture_screen_region(self.bar_region)
                
                # Find indicator position
                indicator_x = self.find_indicator_position(bar_image)
                
                if indicator_x is not None:
                    # Check if in target zone
                    if self.is_in_target_zone(indicator_x):
                        if self.press_space():
                            print(f"✓ Hit target! Indicator at x={indicator_x}")
                    else:
                        print(f"Indicator at x={indicator_x} (not in target zone)", end='\r')
                else:
                    print("Searching for indicator...", end='\r')
                
                time.sleep(self.check_interval)
                
            except Exception as e:
                print(f"Error in detection loop: {e}")
                break
        
        print("\nDetection stopped.")
    
    def toggle_macro(self):
        """Start or stop the macro"""
        if not self.running:
            if not self.bar_region:
                print("Please setup coordinates first (press F9)")
                return
                
            self.running = True
            self.detection_thread = threading.Thread(target=self.detection_loop)
            self.detection_thread.daemon = True
            self.detection_thread.start()
            print("Macro STARTED!")
        else:
            self.running = False
            print("Macro STOPPED!")
    
    def run(self):
        """Main run loop with keyboard controls"""
        print("\nDIG Macro is ready!")
        print("Press F9 to setup coordinates, F8 to start/stop, ESC to exit")
        
        # Setup keyboard hooks
        keyboard.add_hotkey('f8', self.toggle_macro)
        keyboard.add_hotkey('f9', self.setup_coordinates)
        
        try:
            # Keep the program running
            keyboard.wait('esc')
        except KeyboardInterrupt:
            pass
        finally:
            self.running = False
            print("\nExiting DIG Macro...")

def main():
    parser = argparse.ArgumentParser(description='DIG Game Macro')
    parser.add_argument('--interval', type=float, default=0.01, 
                       help='Check interval in seconds (default: 0.01)')
    parser.add_argument('--cooldown', type=float, default=0.2,
                       help='Cooldown between presses in seconds (default: 0.2)')
    
    args = parser.parse_args()
    
    # Disable pyautogui failsafe for gaming
    pyautogui.FAILSAFE = False
    
    macro = DIGMacro()
    macro.check_interval = args.interval
    macro.cooldown_time = args.cooldown
    
    macro.run()

if __name__ == "__main__":
    main()
