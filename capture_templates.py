#!/usr/bin/env python3
"""
Template Capture Tool for DIG Macro - GUI Version
Easy GUI for capturing template images
"""

import tkinter as tk
from tkinter import ttk, messagebox
import cv2
import numpy as np
from PIL import ImageGrab, Image, ImageTk
import os
import threading
import time

class TemplateCaptureGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("DIG Macro - Template Capture Tool")
        self.root.geometry("600x500")
        self.root.resizable(True, True)

        # Variables
        self.current_screenshot = None
        self.templates = {
            "bar": {"captured": False, "image": None, "description": "The entire horizontal progress bar"},
            "indicator": {"captured": False, "image": None, "description": "Just the black moving indicator"},
            "target_zone": {"captured": False, "image": None, "description": "The dark grey target area"}
        }

        self.setup_ui()
        self.check_existing_templates()

    def setup_ui(self):
        """Setup the GUI interface"""
        # Title
        title_label = tk.Label(self.root, text="DIG Macro Template Capture",
                              font=("Arial", 16, "bold"))
        title_label.pack(pady=10)

        # Instructions
        instructions = tk.Text(self.root, height=6, wrap=tk.WORD, bg="#f0f0f0")
        instructions.pack(fill=tk.X, padx=20, pady=10)
        instructions.insert(tk.END,
            "Instructions:\n"
            "1. Make sure the DIG game is visible on your screen\n"
            "2. Click 'Take Screenshot' to capture your screen\n"
            "3. For each template, click the capture button and select the area\n"
            "4. Bar: Select the entire progress bar from left to right\n"
            "5. Indicator: Select just the black moving indicator\n"
            "6. Target Zone: Select the dark grey area where you press SPACE")
        instructions.config(state=tk.DISABLED)

        # Screenshot button
        screenshot_frame = tk.Frame(self.root)
        screenshot_frame.pack(pady=10)

        self.screenshot_btn = tk.Button(screenshot_frame, text="📸 Take Screenshot",
                                       command=self.take_screenshot,
                                       font=("Arial", 12), bg="#4CAF50", fg="white",
                                       width=20, height=2)
        self.screenshot_btn.pack()

        self.screenshot_status = tk.Label(screenshot_frame, text="No screenshot taken",
                                         fg="gray")
        self.screenshot_status.pack(pady=5)

        # Templates frame
        templates_frame = tk.LabelFrame(self.root, text="Template Images",
                                       font=("Arial", 12, "bold"))
        templates_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # Create template capture sections
        for i, (name, info) in enumerate(self.templates.items()):
            self.create_template_section(templates_frame, name, info, i)

        # Bottom buttons
        bottom_frame = tk.Frame(self.root)
        bottom_frame.pack(fill=tk.X, padx=20, pady=10)

        self.run_macro_btn = tk.Button(bottom_frame, text="🚀 Run DIG Macro",
                                      command=self.run_macro,
                                      font=("Arial", 12), bg="#2196F3", fg="white",
                                      state=tk.DISABLED)
        self.run_macro_btn.pack(side=tk.LEFT)

        quit_btn = tk.Button(bottom_frame, text="❌ Exit",
                            command=self.root.quit,
                            font=("Arial", 12))
        quit_btn.pack(side=tk.RIGHT)

        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready - Take a screenshot first")
        status_bar = tk.Label(self.root, textvariable=self.status_var,
                             relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(fill=tk.X, side=tk.BOTTOM)

    def create_template_section(self, parent, name, info, row):
        """Create a section for each template"""
        frame = tk.Frame(parent)
        frame.pack(fill=tk.X, padx=10, pady=5)

        # Template name and description
        name_label = tk.Label(frame, text=f"{name.replace('_', ' ').title()}:",
                             font=("Arial", 10, "bold"), width=12, anchor="w")
        name_label.pack(side=tk.LEFT)

        desc_label = tk.Label(frame, text=info["description"],
                             font=("Arial", 9), fg="gray")
        desc_label.pack(side=tk.LEFT, padx=(10, 0))

        # Capture button
        capture_btn = tk.Button(frame, text="Capture",
                               command=lambda n=name: self.capture_template(n),
                               state=tk.DISABLED, width=10)
        capture_btn.pack(side=tk.RIGHT, padx=5)

        # Status indicator
        status_label = tk.Label(frame, text="❌", font=("Arial", 12))
        status_label.pack(side=tk.RIGHT)

        # Store references
        info["capture_btn"] = capture_btn
        info["status_label"] = status_label

    def check_existing_templates(self):
        """Check if template files already exist"""
        for name, info in self.templates.items():
            filename = f"{name}.png"
            if os.path.exists(filename):
                info["captured"] = True
                info["status_label"].config(text="✅", fg="green")

        self.update_run_button()

    def take_screenshot(self):
        """Take a screenshot of the entire screen"""
        self.status_var.set("Taking screenshot...")
        self.root.update()

        # Take screenshot
        screenshot = ImageGrab.grab()
        self.current_screenshot = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)

        # Store original dimensions for scaling calculations
        self.original_height, self.original_width = self.current_screenshot.shape[:2]

        # Create a scaled version for display (fit to screen)
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        # Scale to fit 80% of screen size
        max_width = int(screen_width * 0.8)
        max_height = int(screen_height * 0.8)

        # Calculate scaling factor
        scale_x = max_width / self.original_width
        scale_y = max_height / self.original_height
        self.scale_factor = min(scale_x, scale_y, 1.0)  # Don't upscale

        # Create scaled image for display
        if self.scale_factor < 1.0:
            new_width = int(self.original_width * self.scale_factor)
            new_height = int(self.original_height * self.scale_factor)
            self.display_screenshot = cv2.resize(self.current_screenshot, (new_width, new_height))
        else:
            self.display_screenshot = self.current_screenshot.copy()
            self.scale_factor = 1.0

        # Enable capture buttons
        for info in self.templates.values():
            info["capture_btn"].config(state=tk.NORMAL)

        self.screenshot_status.config(text="✅ Screenshot ready!", fg="green")
        self.status_var.set("Screenshot taken - Now capture templates")

    def capture_template(self, name):
        """Capture a specific template"""
        if self.current_screenshot is None:
            messagebox.showerror("Error", "Please take a screenshot first!")
            return

        info = self.templates[name]
        self.status_var.set(f"Capturing {name}... Select the area in the popup window")

        try:
            # Use the scaled display image for selection
            roi = cv2.selectROI(f"Select {name.replace('_', ' ').title()}",
                               self.display_screenshot, False)
            cv2.destroyAllWindows()

            print(f"DEBUG: Selected ROI: {roi}")

            if roi[2] == 0 or roi[3] == 0:
                self.status_var.set("Capture cancelled - no area selected")
                print("DEBUG: No area selected")
                return

            # Scale the ROI coordinates back to original image size
            x, y, w, h = roi
            print(f"DEBUG: Original ROI: x={x}, y={y}, w={w}, h={h}")
            print(f"DEBUG: Scale factor: {self.scale_factor}")

            if self.scale_factor < 1.0:
                x = int(x / self.scale_factor)
                y = int(y / self.scale_factor)
                w = int(w / self.scale_factor)
                h = int(h / self.scale_factor)
                print(f"DEBUG: Scaled ROI: x={x}, y={y}, w={w}, h={h}")

            # Check bounds
            orig_h, orig_w = self.current_screenshot.shape[:2]
            print(f"DEBUG: Original image size: {orig_w}x{orig_h}")

            if x < 0 or y < 0 or x+w > orig_w or y+h > orig_h:
                print(f"DEBUG: ROI out of bounds!")
                self.status_var.set("Error: Selection out of bounds")
                return

            # Extract template from original screenshot
            template = self.current_screenshot[y:y+h, x:x+w]
            print(f"DEBUG: Template shape: {template.shape}")

            if template.size == 0:
                print("DEBUG: Template is empty!")
                self.status_var.set("Error: Empty template")
                return

            filename = f"{name}.png"
            success = cv2.imwrite(filename, template)
            print(f"DEBUG: cv2.imwrite returned: {success}")
            print(f"DEBUG: File exists after write: {os.path.exists(filename)}")

            if not success:
                self.status_var.set(f"Error: Failed to save {filename}")
                return

            # Update UI
            info["captured"] = True
            info["image"] = template
            info["status_label"].config(text="✅", fg="green")

            self.status_var.set(f"✅ {name} captured and saved as {filename}!")
            self.update_run_button()

            # Show preview
            self.show_preview(name, template)

        except Exception as e:
            print(f"DEBUG: Exception in capture_template: {e}")
            self.status_var.set(f"Error: {str(e)}")
            messagebox.showerror("Capture Error", f"Failed to capture template: {str(e)}")

    def show_preview(self, name, template):
        """Show a preview of the captured template"""
        preview_window = tk.Toplevel(self.root)
        preview_window.title(f"Preview: {name}")
        preview_window.geometry("400x300")

        # Convert for tkinter
        template_rgb = cv2.cvtColor(template, cv2.COLOR_BGR2RGB)
        pil_image = Image.fromarray(template_rgb)

        # Resize if too large
        max_size = (350, 250)
        pil_image.thumbnail(max_size, Image.Resampling.LANCZOS)

        photo = ImageTk.PhotoImage(pil_image)

        label = tk.Label(preview_window, image=photo)
        label.image = photo  # Keep a reference
        label.pack(expand=True)

        close_btn = tk.Button(preview_window, text="Close",
                             command=preview_window.destroy)
        close_btn.pack(pady=10)

    def update_run_button(self):
        """Enable run button if all templates are captured"""
        all_captured = all(info["captured"] for info in self.templates.values())
        if all_captured:
            self.run_macro_btn.config(state=tk.NORMAL)
            self.status_var.set("✅ All templates ready! You can run the macro now.")

    def run_macro(self):
        """Run the main DIG macro"""
        if messagebox.askyesno("Run Macro", "Start the DIG macro now?\n\nThe macro will run in a new window.\nPress F8 to start/stop, ESC to exit."):
            # Run the macro in a separate thread
            threading.Thread(target=self.launch_macro, daemon=True).start()

    def launch_macro(self):
        """Launch the macro script"""
        try:
            import subprocess
            subprocess.Popen(["python", "dig_macro.py"])
        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch macro: {e}")

    def run(self):
        """Start the GUI"""
        self.root.mainloop()

def main():
    app = TemplateCaptureGUI()
    app.run()

if __name__ == "__main__":
    main()
