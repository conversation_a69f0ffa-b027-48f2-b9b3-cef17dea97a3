#!/usr/bin/env python3
"""
Template Capture Tool for DIG Macro
Helps you easily capture the required template images
"""

import cv2
import numpy as np
from PIL import ImageGrab
import keyboard

def capture_template(name, description):
    """Capture a template image by taking a screenshot and letting user select area"""
    print(f"\n=== Capturing {name} ===")
    print(f"Description: {description}")
    print("Make sure the DIG game is visible on screen.")
    input("Press ENTER when ready to capture...")
    
    # Take screenshot
    screenshot = ImageGrab.grab()
    screenshot_np = np.array(screenshot)
    screenshot_cv = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)
    
    # Let user select region
    print(f"Select the {name} area in the window that opens...")
    roi = cv2.selectROI(f"Select {name}", screenshot_cv, False)
    cv2.destroyAllWindows()
    
    if roi[2] == 0 or roi[3] == 0:
        print("Invalid selection. Skipping...")
        return False
    
    # Extract the selected region
    x, y, w, h = roi
    template = screenshot_cv[y:y+h, x:x+w]
    
    # Save the template
    filename = f"{name}.png"
    cv2.imwrite(filename, template)
    print(f"✓ Saved {filename}")
    
    # Show preview
    cv2.imshow(f"Preview: {filename}", template)
    print("Press any key to continue...")
    cv2.waitKey(0)
    cv2.destroyAllWindows()
    
    return True

def main():
    print("DIG Macro Template Capture Tool")
    print("=" * 40)
    print("This tool will help you capture the 3 template images needed:")
    print("1. bar.png - The entire progress bar")
    print("2. indicator.png - Just the black moving indicator")
    print("3. target_zone.png - The dark grey target area")
    print()
    print("Tips:")
    print("- Make sure the game is visible and the bar is showing")
    print("- For the indicator, capture it when it's clearly visible")
    print("- For the target zone, capture just the dark grey area")
    print("- Be precise with your selections!")
    
    templates = [
        ("bar", "The entire horizontal progress bar from left to right edge"),
        ("indicator", "Just the black moving indicator (small rectangle)"),
        ("target_zone", "The dark grey target area where you need to press SPACE")
    ]
    
    for name, description in templates:
        success = capture_template(name, description)
        if not success:
            print(f"Failed to capture {name}. You can run this script again to retry.")
    
    print("\n" + "=" * 40)
    print("Template capture complete!")
    print("You can now run: python dig_macro.py")
    print("Press F8 to start the macro, ESC to exit")

if __name__ == "__main__":
    main()
